defmodule BuyerboardBackendWeb.ImageView do
  use BuyerboardBackendWeb, :view
  alias BuyerboardBackendWeb.ImageView

  def render("show.json", %{image: image}) do
    message = %{title: nil, body: "Successfully Uploaded image"}
    %{data: render_one(image, ImageView, "image.json"), message: message}
  end

  def render("image.json", %{image: image}) do
    %{
      image_url: get_url(image.image.file_name)
    }
  end

  def render("error.json", %{error: error}) do
    %{error: error}
  end

  defp get_url(image) do
    BuyerboardBackendWeb.Endpoint.url() <> BuyerboardBackend.ImageUploader.url(image)
  end
end
