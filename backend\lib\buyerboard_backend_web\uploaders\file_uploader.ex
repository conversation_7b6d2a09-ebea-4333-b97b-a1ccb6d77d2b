defmodule BuyerboardBackend.FileUploader do
  use Arc.Definition
  use Arc.Ecto.Definition

  @versions [:original]

  @doc """
  Define the storage method. Set this to `Arc.Storage.Local` for local storage
  or `Arc.Storage.S3` if using Amazon S3.
  """
  def __storage, do: Arc.Storage.Local

  @doc """
  Validate file type by allowing only specific extensions.
  """
  def validate({file, _}) do
    ~w(.jpg .jpeg .gif .png .mp4 .pdf .doc .mp3) |> Enum.member?(Path.extname(file.file_name))
  end

  @doc """
  Override the storage directory based on file scope, if needed.
  """

end
