class Endpoints {
  const Endpoints._();

  ///Api version & directories
  // static const _apiVersion = 'api/v1';
  static const login = '/users/log_in';
  static const register = '/users/register';
  static const socialAuth = '/social-sign-up';
  static const resetPassword = '/users/reset_password';
  static const verifyOtp = '/users/reset_password/verify_token';
  static const buyer = '/buyer';
  static const editBuyer = '/buyer/{id}';
  static const profile = '/users/profile';
  static const uploadImage = '/upload_image';
  static const uploadAttachment = '/upload_attachment';
  static const deleteAttachment = '/delete_attachments';
  static const logOut = '/users/log_out';
  static const getAllBuyers = '/buyers';
  static const getMyBuyers = '/user_buyers';
  static const getOtherBuyers = '/other_buyers';
  static const getFavouriteBuyers = '/favourite_buyers';
  static const favouriteBuyer = '/favourite_buyer/{id}';
  static const searchBuyers = '/search_buyers/{zipCode}';
  static const filteredBuyers = '/filtered_buyers';
  static const getAddressFromZipCode = '/location/{zipCode}';
  static const updateNote = '/buyer_note/{buyerId}';
  static const appleLogin = '/apple-sign-up';
  static const buyersLocation = '/buyer_locations';
  static const getBuyer = '/buyer/{id}';
  static const deleteBuyer = '/buyer/{id}';
  static const deleteUser = '/user/delete';
}
