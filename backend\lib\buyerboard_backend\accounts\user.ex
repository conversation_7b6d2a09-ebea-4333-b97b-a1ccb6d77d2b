defmodule BuyerboardBackend.Accounts.User do
  use Ecto.Schema
  use Arc.Ecto.Schema
  import Ecto.Changeset
  alias BuyerboardBackend.{Buyer, <PERSON><PERSON>, Buyers, Thread, Accounts.AuthProvider, Profile}

  @derive {Jason.Encoder,
           only: [
             :id,
             :email,
             :password,
             :hashed_password,
             :confirmed_at,
             :favourite_buyers,
             :profile
           ]}
  schema "users" do
    field :email, :string
    field :password, :string, virtual: true, redact: true
    field :hashed_password, :string, redact: true
    field :confirmed_at, :naive_datetime
    field :apple_identifier, :string
    field :favourite_buyers, {:array, :integer}, default: []
    field :is_active, :boolean, default: nil

    many_to_many(:threads, Thread,
      join_through: "threads_users",
      on_replace: :delete
    )

    has_one(:profile, BuyerboardBackend.Profile)
    has_many(:buyers, Buyer)
    has_many(:notes, BuyerboardBackend.Note)
    has_many :auth_providers, AuthProvider

    timestamps(type: :utc_datetime)
  end

  @doc """
  A user changeset for registration.

  It is important to validate the length of both email and password.
  Otherwise databases may truncate the email without warnings, which
  could lead to unpredictable or insecure behaviour. Long passwords may
  also be very expensive to hash for certain algorithms.

  ## Options

    * `:hash_password` - Hashes the password so it can be stored securely
      in the database and ensures the password field is cleared to prevent
      leaks in the logs. If password hashing is not needed and clearing the
      password field is not desired (like when using this changeset for
      validations on a LiveView form), this option can be set to `false`.
      Defaults to `true`.

    * `:validate_email` - Validates the uniqueness of the email, in case
      you don't want to validate the uniqueness of the email (like when
      using this changeset for validations on a LiveView form before
      submitting the form), this option can be set to `false`.
      Defaults to `true`.
  """


  def registration_changeset(user, attrs, opts \\ []) do

    # Normalize email key access for compatibility with atom and string keys
    email = Map.get(attrs, :email) || Map.get(attrs, "email")
    first_name = Map.get(attrs, :first_name) || Map.get(attrs, "first_name")
    last_name = Map.get(attrs, :last_name) || Map.get(attrs, "last_name")

    user
    |> cast(attrs, [:email, :password])
    |> cast_assoc(:profile, required: false, with: &profile_changeset/2)
    |> put_assoc(:profile, %Profile{
      first_name: first_name,
      last_name: last_name,
      agent_email: email && String.downcase(email) # Ensure `email` is not nil
    })
    |> update_change(:email, &String.downcase/1)
    |> validate_email(opts)
    |> validate_password(opts)
  end

  def social_registration_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [:email])
    # Allow profile to be created
    |> cast_assoc(:profile, required: false, with: &profile_changeset/2)
    |> then(fn changeset ->
      changeset
      |> put_assoc(:auth_providers, [
        %AuthProvider{
          provider: "google"
        }
      ])
      |> put_assoc(:profile, %Profile{
        first_name: Map.get(attrs, "first_name"),
        last_name: Map.get(attrs, "last_name")
      })
    end)
    |> validate_email(opts)
  end

  def apple_registration_changeset(user, attrs, opts \\ [])

  def apple_registration_changeset(user, attrs, _opts) do
    user
    |> cast(attrs, [:email, :apple_identifier])
    # Allow profile to be created
    |> cast_assoc(:profile, required: false, with: &profile_changeset/2)
    |> then(fn changeset ->
      changeset
      |> put_assoc(:auth_providers, [
        %AuthProvider{
          provider: "apple",
          apple_identifier: Map.get(attrs, "apple_identifier")
        }
      ])
      |> put_assoc(:profile, %Profile{
        first_name: Map.get(attrs, "first_name"),
        last_name: Map.get(attrs, "last_name")
      })
    end)
  end

  def generate_password(length \\ 20) do
    :crypto.strong_rand_bytes(length) |> Base.encode64() |> binary_part(0, length)
  end

  # def profile_changeset(user, attrs, opts \\ []) do
  #   user
  #   |> cast(attrs, [
  #     :first_name,
  #     :last_name,
  #     :email,
  #     :phone_number_primary,
  #     :image_url,
  #     :brokerage_name,
  #     :brokerage_lisence_no,
  #     :lisence_id_no,
  #     :broker_street_address,
  #     :broker_city,
  #     :brokerage_zip_code,
  #     :brokerage_state
  #   ])
  #   |> validate_required([
  #     :first_name,
  #     :last_name,
  #     :lisence_id_no,
  #     :brokerage_name,
  #     :brokerage_lisence_no,
  #     :phone_number_primary
  #   ])
  #   |> validate_email(opts)
  #   |> unsafe_validate_unique(:email, BuyerboardBackend.Repo)
  #   |> unique_constraint(:email)
  # end

  defp validate_email(changeset, opts) do
    changeset
    |> validate_required([:email])
    |> validate_format(:email, ~r/^[^\s]+@[^\s]+$/, message: "must have the @ sign and no spaces")
    |> validate_length(:email, max: 160)
    |> unique_constraint(:email, message: "Email already exists. Sign-in instead.")

    #    |> maybe_validate_unique_email(opts)
  end

  defp validate_password(changeset, opts) do
    changeset
    |> validate_required([:password])
    |> validate_length(:password, min: 8, max: 72)
    # Examples of additional password validation:
    # |> validate_format(:password, ~r/[a-z]/, message: "at least one lower case character")
    # |> validate_format(:password, ~r/[A-Z]/, message: "at least one upper case character")
    |> validate_format(:password, ~r/[!@#$%^&*(),.?":{}|<>]/,
      message: "at least one special character"
    )
    |> maybe_hash_password(opts)
  end

  defp maybe_hash_password(changeset, opts) do
    hash_password? = Keyword.get(opts, :hash_password, true)
    password = get_change(changeset, :password)

    if hash_password? && password && changeset.valid? do
      changeset
      # If using Bcrypt, then further validate it is at most 72 bytes long
      |> validate_length(:password, max: 72, count: :bytes)
      # Hashing could be done with `Ecto.Changeset.prepare_changes/2`, but that
      # would keep the database transaction open longer and hurt performance.
      |> put_change(:hashed_password, Bcrypt.hash_pwd_salt(password))
      |> delete_change(:password)
    else
      changeset
    end
  end

  defp maybe_validate_unique_email(changeset, opts) do
    if Keyword.get(opts, :validate_email, true) do
      changeset
      |> unsafe_validate_unique(:email, BuyerboardBackend.Repo)
      |> unique_constraint(:email, message: "Email already exists. Sign-in instead.")
    else
      changeset
    end
  end

  @doc """
  A user changeset for changing the email.

  It requires the email to change otherwise an error is added.
  """
  def email_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [:email])
    |> validate_email(opts)
    |> case do
      %{changes: %{email: _}} = changeset -> changeset
      %{} = changeset -> add_error(changeset, :email, "did not change")
    end
  end

  @doc """
  A user changeset for changing the password.

  ## Options

    * `:hash_password` - Hashes the password so it can be stored securely
      in the database and ensures the password field is cleared to prevent
      leaks in the logs. If password hashing is not needed and clearing the
      password field is not desired (like when using this changeset for
      validations on a LiveView form), this option can be set to `false`.
      Defaults to `true`.
  """
  def password_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [:password])
    |> validate_confirmation(:password, message: "does not match password")
    |> validate_password(opts)
  end

  @doc """
  Confirms the account by setting `confirmed_at`.
  """
  def confirm_changeset(user) do
    now = NaiveDateTime.utc_now() |> NaiveDateTime.truncate(:second)
    change(user, confirmed_at: now)
  end

  @doc """
  Verifies the password.

  If there is no user or the user doesn't have a password, we call
  `Bcrypt.no_user_verify/0` to avoid timing attacks.
  """
  def valid_password?(
        %BuyerboardBackend.Accounts.User{hashed_password: hashed_password},
        password
      )
      when is_binary(hashed_password) and byte_size(password) > 0 do
    Bcrypt.verify_pass(password, hashed_password)
  end

  def valid_password?(_, _) do
    Bcrypt.no_user_verify()
    false
  end

  @doc """
  Validates the current password otherwise adds an error to the changeset.
  """
  def validate_current_password(changeset, password) do
    if valid_password?(changeset.data, password) do
      changeset
    else
      add_error(changeset, :current_password, "is not valid")
    end
  end
  def get_users_favourite_buyers(id) do
    user = Repo.get(__MODULE__, id)
    Enum.map(user.favourite_buyers, &Buyers.get_buyer(&1))
  end

#  def get_users_favourite_buyers(id) do
#    case Repo.get(__MODULE__, id) do
#      nil ->
#        [] # Return an empty list if the user does not exist
#
#      user ->
#        user.favourite_buyers
#        |> Enum.map(&Buyers.get_buyer(&1))
#        |> Enum.filter(& &1) # Filter out nil buyers
#    end
#  end

  def get_favourite_buyers_count(user_id) do
    get_users_favourite_buyers(user_id) |> Enum.count()
  end

  def update_buyers_favourite(user_id, buyer_id, is_favourite) do
    user = Repo.get(__MODULE__, user_id)
    favourite_buyers = user.favourite_buyers

    changeset =
      if is_favourite do
        favourite_buyers = (favourite_buyers ++ [buyer_id]) |> Enum.uniq()
        Ecto.Changeset.change(user, favourite_buyers: favourite_buyers)
      else
        favourite_buyers = (favourite_buyers -- [buyer_id]) |> Enum.uniq()
        Ecto.Changeset.change(user, favourite_buyers: favourite_buyers)
      end

    Repo.update(changeset)
  end

  defp profile_changeset(profile, attrs) do
    profile
    |> cast(attrs, [:first_name, :last_name])
    |> validate_required([:first_name, :last_name])
  end

  #  defp validate_optional_email(changeset) do
  #    # Only validate if an email is provided, otherwise skip it
  #    if get_field(changeset, :email) do
  #      changeset
  #      |> validate_format(:email, ~r/^[^\s]+@[^\s]+$/, message: "must have the @ sign and no spaces")
  #      |> validate_length(:email, max: 160)
  #    else
  #      changeset
  #    end
  #  end
end
