import 'package:buyer_board/features/add_buyer/data/models/buyer_model.dart';
import 'package:flutter/material.dart';

sealed class BuyersEvent {}

final class LoadBuyers extends BuyersEvent {
  final bool forceRefresh;
  final BuildContext context;
  final int? page;
  LoadBuyers({this.forceRefresh = false, required this.context, this.page});
}

final class ClearList extends BuyersEvent {}

final class ClearSearch extends BuyersEvent {}

final class ClearFilters extends BuyersEvent {
  final String? zipCode;

  ClearFilters({this.zipCode});
}

final class UpdateBuyer extends BuyersEvent {
  final BuyerModel buyer;

  UpdateBuyer({required this.buyer});
}

final class DeleteBuyer extends BuyersEvent {
  final int buyerId;

  DeleteBuyer({required this.buyerId});
}
