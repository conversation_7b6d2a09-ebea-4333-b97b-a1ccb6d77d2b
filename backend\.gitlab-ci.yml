default:
  image: elixir:latest
  cache:
    key: ${CI_COMMIT_REF_NAME}
    paths:
      - deps/
      - _build/

  before_script:
    - export MIX_ENV=prod
    - git remote remove origin-ci || true
    - git remote add origin-ci https://hammad:$<EMAIL>/buyerboard/buyerboard_backend
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "CI"
    - mix local.rebar --force
    - mix local.hex --force

variables:
  DOCKER_HOST: tcp://docker:2375
  DOCKER_TLS_CERTDIR: ""

  # variables for the postgres service container used in the test stage
  POSTGRES_USER: postgres
  POSTGRES_PASSWORD: postgres
  POSTGRES_DB: buyerboard_test
  DB_HOST: postgres
  DATABASE_URL: "ecto://postgres:postgres@postgres/buyerboard_backend_test"
  SECRET_KEY_BASE: $SECRET_KEY_BASE

stages:
  - test
  - static-analysis

  - release
  - release-prod
   
  - build-docker
  - build-docker-prod

  - notify
  - notify-prod

test:
  environment:
    name: test
  stage: test

  services:
    - name: postgis/postgis:latest
      alias: postgres

  variables:
    MIX_ENV: test
    ONESIGNAL_URL: "https://onesignal.com/api/v1/notifications"
    ONESIGNAL_APP_ID: $ONESIGNAL_APP_ID
    ONESIGNAL_API_KEY: $ONESIGNAL_API_KEY

  script:
    - export MIX_ENV=test
    - export ONESIGNAL_APP_ID=$ONESIGNAL_APP_ID
    - export ONESIGNAL_API_KEY=$ONESIGNAL_API_KEY
    - export ONESIGNAL_URL="https://onesignal.com/api/v1/notifications"

    - mix deps.get
    - mix phx.digest
    # - mix sentry.package_source_code
    - mix ecto.create --quiet
    - mix ecto.migrate --quiet
    - mix test --trace

credo:
  stage: static-analysis
  allow_failure: true
  dependencies:
  - test
  
  script:
    - export MIX_ENV=test
    - mix deps.get
    - mix phx.digest
    - mix credo || true

dialyzer:
  stage: static-analysis
  allow_failure: true
  dependencies:
  - test
  
  script:
    - export MIX_ENV=dev
    - mix deps.get
    - mix phx.digest
    - mix dialyzer || true


build-docker:
  stage: build-docker
  environment:
    name: staging

  image: docker:20.10.16-dind
  services:
    - docker:20.10.16-dind

  dependencies:
    - test

  before_script: []
  script:
    - echo "$CI_REGISTRY_PASSWORD" | docker login $CI_REGISTRY -u $CI_REGISTRY_USER --password-stdin

    - apk add git
    - git remote remove origin-ci || true
    - git remote add origin-ci https://hammad:$<EMAIL>/buyerboard/buyerboard_backend

    # deleting branch cuz it don't purge repo caches somehow
    - git branch -D staging || true
    - git fetch origin-ci staging

    - git checkout --track origin-ci/staging

    - docker build -t $CI_REGISTRY/buyerboard/buyerboard_backend/buyerboard-backend-staging:$CI_COMMIT_SHORT_SHA -t $CI_REGISTRY/buyerboard/buyerboard_backend/buyerboard-backend-staging:latest .
    - docker push $CI_REGISTRY/buyerboard/buyerboard_backend/buyerboard-backend-staging:$CI_COMMIT_SHORT_SHA
    - docker push $CI_REGISTRY/buyerboard/buyerboard_backend/buyerboard-backend-staging:latest
  only:
    - main

release:
  environment:
    name: staging

  stage: release

  dependencies:
    - test

  script:
    # - git fetch origin-ci main && git checkout --track origin-ci/main
    # - mix local.rebar --force
    # - mix local.hex --force
    - mix deps.get --only prod
    - mix sentry.package_source_code

    # - git checkout -b ci-release
    - git stash

    # deleting branch cuz it don't purge repo caches somehow
    - git branch -D staging || true
    - git fetch origin-ci staging && git checkout --track origin-ci/staging
    - git pull --no-edit --strategy-option=theirs --no-ff origin-ci main --allow-unrelated-histories
    # - git stash apply

    - git add .
    - git diff-index --quiet HEAD || git commit -m "CI Triggered Release
      This commit contains the Phoenix release built by the CI pipeline."

    # - rm ./priv/static/swagger.json

    # - git merge ci-release
    - git push origin-ci staging --force

  only:
    - main

notify:
  environment:
    name: staging

  stage: notify
  script:
    - 'curl --request POST --header "X-Secret-Header: $SECRET_HEADER_VALUE" https://bb.vdev.tech/webhook/redeploy-backend'
  only:
    - main

#
# production stages only, please mind the 'staging' conventions in 'production' ...
#

build-docker-prod:
  stage: build-docker-prod
  environment:
    name: staging

  image: docker:20.10.16-dind
  services:
    - docker:20.10.16-dind

  before_script: []
  script:
    - echo "$CI_REGISTRY_PASSWORD" | docker login $CI_REGISTRY -u $CI_REGISTRY_USER --password-stdin

    - apk add git
    - git remote remove origin-ci || true
    - git remote add origin-ci https://hammad:$<EMAIL>/buyerboard/buyerboard_backend

    # deleting branch cuz it don't purge repo caches somehow
    - git branch -D staging || true
    - git fetch origin-ci production
    - git checkout --track origin-ci/production

    - docker build -t $CI_REGISTRY/buyerboard/buyerboard_backend/buyerboard-backend-production:$CI_COMMIT_SHORT_SHA -t $CI_REGISTRY/buyerboard/buyerboard_backend/buyerboard-backend-production:latest .
    - docker push $CI_REGISTRY/buyerboard/buyerboard_backend/buyerboard-backend-production:$CI_COMMIT_SHORT_SHA
    - docker push $CI_REGISTRY/buyerboard/buyerboard_backend/buyerboard-backend-production:latest
  only:
    - production

release-prod:
  environment:
    name: staging

  stage: release-prod
  script:
    - git fetch origin-ci production && git checkout --track origin-ci/production
    - mix local.rebar --force
    - mix local.hex --force
    - mix deps.get --only prod

    # - mix phx.gen.release --docker
    # - rm ./priv/static/swagger.json
    # - mix phx.swagger.generate

    # - git checkout -b ci-release

    - git add .
    - git diff-index --quiet HEAD || git commit -m "CI Triggered Release
      This commit contains the Phoenix release built by the CI pipeline."

    # - git fetch origin-ci production && git checkout --track origin-ci/production

    # - rm ./priv/static/swagger.json

    # - git merge ci-release
    - git push origin-ci production --force

  only:
    - production

notify-prod:
  environment:
    name: staging

  stage: notify-prod
  script:
    - 'curl --request POST --header "X-Secret-Header: $SECRET_HEADER_VALUE" https://bba.vdev.tech/webhook/redeploy-backend'
  only:
    - production
