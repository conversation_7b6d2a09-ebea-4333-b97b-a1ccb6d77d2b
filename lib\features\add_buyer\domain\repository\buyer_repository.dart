import '../../../../core/network/base_response.dart';
import '../../data/models/buyer_model.dart';

abstract class BuyerRepository {
  Future<BaseResponse<BuyerModel>> addBuyer({required BuyerModel buyer});
  Future<BaseResponse<BuyerModel>> editBuyer(
      {required BuyerModel buyer, required int id});
  Future<BaseResponse<BuyerModel>> favouriteBuyer(
      {required bool isFavourite, required int id});
  Future<BaseResponse<List<BuyerModel>>> getAllBuyers();
  Future<BaseResponse<List<BuyerModel>>> getFavouriteBuyers();
  Future<BaseResponse<List<BuyerModel>>> getMyBuyers();
  Future<BaseResponse<List<BuyerModel>>> getOtherBuyers();
  Future<BaseResponse<List<BuyerModel>>> getBuyersByZipCode(String zipCode);
  Future<BuyerModel> updateNote({
    required int id,
    required String note,
  });
  Future<BaseResponse<BuyerModel>> getBuyer(int id);
  Future<BaseResponse> deleteBuyer({required int id});
}
