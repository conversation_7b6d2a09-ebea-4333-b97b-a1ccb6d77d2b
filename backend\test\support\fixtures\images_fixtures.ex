defmodule BuyerboardBackend.ImagesFixtures do
  @moduledoc """
  This module defines test helpers for creating
  entities via the `BuyerboardBackend.Images` context.
  """

  @doc """
  Generate a image.
  """
  def image_fixture(attrs \\ %{}) do
    {:ok, image} =
      attrs
      |> Enum.into(%{
        image: "some image"
      })
      |> BuyerboardBackend.Images.create_image()

    image
  end
end
