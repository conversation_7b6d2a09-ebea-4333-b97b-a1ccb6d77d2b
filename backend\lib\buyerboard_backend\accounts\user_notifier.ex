defmodule BuyerboardBackend.Accounts.UserNotifier do
  use BuyerboardBackend.Notifiers
  require Logger
  alias Buy<PERSON><PERSON>B<PERSON><PERSON>.MobileNotifier

  import Bamboo.{Email}
  # Delivers the email using the application mailer.
  # defp deliver(recipient, subject, body) do
  #   email =
  #     new()
  #     |> to(recipient)
  #     |> from({"BuyerboardBackend", "<EMAIL>"})
  #     |> subject(subject)
  #     |> text_body(body)

  #   with {:ok, _metadata} <- Mailer.deliver(email) do
  #     {:ok, email}
  #   end
  # end

  @doc """
  Deliver instructions to reset a user password.
  """

  #  def deliver_reset_password_instructions(user, otp) do
  #    # Safely access the first_name from profile or default to "User"
  #    user_name = if user.profile && user.profile.first_name, do: user.profile.first_name, else: "User"
  #
  #    %Bamboo.Email{
  #      subject: "Reset Your Password for BuyerBoard",
  #      html_body: """
  #      <p>Hi #{user_name},</p>
  #
  #      <p>We received a request to reset your password for your BuyerBoard account. If you did not make this request, please ignore this email. Otherwise, please follow the instructions below to reset your password:</p>
  #
  #      <p>Your OTP for reset is <strong>#{otp}</strong></p>
  #
  #      <p>If you have any issues or did not request a password reset, please contact our support team at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
  #
  #      <p>Thank you for using BuyerBoard!</p>
  #
  #      <p>Best regards,<br/>
  #      The BuyerBoard Team</p>
  #      """,
  #      text_body: """
  #      Hi #{user_name},
  #
  #      We received a request to reset your password for your BuyerBoard account. If you did not make this request, please ignore this email. Otherwise, please follow the instructions below to reset your password:
  #
  #      Your OTP for reset is #{otp}
  #
  #      If you have any issues or did not request a password reset, please contact our support <NAME_EMAIL>.
  #
  #      Thank you for using BuyerBoard!
  #
  #      Best regards,
  #      The BuyerBoard Team
  #      """,
  #      blocked: false
  #    }
  #    |> deliver_transactional_email(user)
  #  end

  def deliver_account_deleted_email(user) do
    # Safely access the first_name from profile or default to "User"
    user_name =
      if user.profile && user.profile.first_name, do: user.profile.first_name, else: "User"

    %Bamboo.Email{
      subject: "User Account Deleted",
      html_body: """
      <p>The User Name: #{user_name}, Account Has been deleted</p>

      """,
      text_body: """
      The User Name: #{user_name}, Account Has been deleted,

      """,
      blocked: false
    }
    |> deliver_transactional_delete_email(user)
  end

  def deliver_account_deleted_email_link(user, link) do
    # Safely access the first_name from profile or default to "User"
    user_name =
      if user.profile && user.profile.first_name, do: user.profile.first_name, else: "User"

    %Bamboo.Email{
      subject: "User Account Deletion",
      html_body: """
      <p>Hi #{user_name}, this is the link to delete you account</p>
      <p>#{link}</p>

      """,
      text_body: """
      Hi #{user_name}, this is the link to delete you account,
      #{link}

      """,
      blocked: false
    }
    |> deliver_transactional_email(user)
  end

  def deliver_reset_password_instructions(user, otp) do
    first_name = (user.profile && user.profile.first_name) || "User"

    sendgrid_template(:password_reset_template, Name: first_name, OTP: otp)
    #    MobileNotifier.send_push_notification("27", "reset", "password has been reset")
    |> to(user.email)
    |> from(noreply_address())
    |> deliver_now()
  end

  defp deliver_transactional_email(params, user) do
    params
    |> to(user.email)
    |> from(noreply_address())
    |> deliver_now()
  end

  defp deliver_transactional_delete_email(params, user) do
    params
    |> to(noreply_address())
    |> from(noreply_address())
    |> deliver_now()
  end
end
