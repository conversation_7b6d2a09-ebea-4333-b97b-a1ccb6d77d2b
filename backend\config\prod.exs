import Config

# Note we also include the path to a cache manifest
# containing the digested version of static files. This
# manifest is generated by the `mix assets.deploy` task,
# which you should run after static files are built and
# before starting your production server.
config :buyerboard_backend, BuyerboardBackendWeb.Endpoint,
  cache_static_manifest: "priv/static/cache_manifest.json"

# Configures Swoosh API Client
config :swoosh, api_client: Swoosh.ApiClient.Finch, finch_name: BuyerboardBackend.Finch

# Disable Swoosh Local Memory Storage
config :swoosh, local: false

# Do not print debug messages in production
config :logger, level: :info

config :buyerboard_backend, BuyerboardBackend.Mailer,
  adapter: Bamboo.SendGridAdapter,
  api_key: "*********************************************************************",
  generic_transactional_template: "d-5879aaa8ecaf4eceabc76a4a017069c0",
  password_reset_template: "d-2b58618abc304abc85fe7b40d5e0c4f4",
  email_template: "d-c254e486bf4c430781956dfb2ecf511c",
  no_reply_email: "<EMAIL>",
  reply_to_domain: "dev-inbox.buyerboard.com",
  hackney_opts: [
    recv_timeout: :timer.minutes(1)
  ]

config :sentry,
  dsn: "https://<EMAIL>/4",
  environment_name: Mix.env(),
  enable_source_code_context: true,
  root_source_code_paths: [File.cwd!()]

# Runtime production configuration, including reading
# of environment variables, is done on config/runtime.exs.
