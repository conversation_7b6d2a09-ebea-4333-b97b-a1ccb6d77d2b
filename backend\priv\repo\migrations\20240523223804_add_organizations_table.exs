defmodule BuyerboardBackend.Repo.Migrations.AddOrganizationsTable do
  use Ecto.Migration

  def up do
    create table(:organizations) do
      add :company_name, :string
      add :real_estate_lisence_no, :string
      add :broker_lisence_no, :string
      add :state, :string
      add :zip_codes, {:array, :integer}
      add :search_range, :string

      add :user_id, references(:users, on_delete: :delete_all), null: false

      timestamps(type: :utc_datetime)
    end
  end

  def down do
    drop(table(:organizations))
  end
end
