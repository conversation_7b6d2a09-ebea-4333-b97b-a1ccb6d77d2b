defmodule BuyerboardBackendWeb.BuyerControllerTest do
  use BuyerboardBackendWeb.ConnCase

  alias BuyerboardBackend.{Buyer, Repo, Accounts.User, BuyerNeed}

  @valid_user %{
    email: "<EMAIL>",
    password: "Media2002,",
    first_name: "<PERSON>",
    last_name: "<PERSON><PERSON>"
  }

  @buyer_params %{
    additional_requests: ["dddd"],
    buyer_expiration_date: "2025-12-26 11:08:48Z",
    buyer_locations_of_interest: ["02134"],
    buyer_need: %{
      budget_upto: "100k",
      financial_status: "pre_qualified",
      min_area: "1000.0",
      min_bathrooms: "3",
      min_bedrooms: "2",
      property_type: "apartment",
      purchase_type: "purchase"
    },
    email: "<EMAIL>",
    first_name: "Second",
    image_url: "/image.png",
    is_favourite: false,
    last_name: "Buyer",
    note: "This is my favourite buyer and I am note for it",
    primary_phone_number: "+12345",
    timezone_offset: 5
  }

  setup do
    # Insert a user
    user = Repo.insert!(%User{} |> User.registration_changeset(@valid_user))

    # Log in the user to get the authentication token
    conn = build_conn() # Creates the connection object
    login_response = post(conn, ~p"/users/log_in", %{
      "email" => @valid_user[:email],
      "password" => @valid_user[:password]
    })

    token = json_response(login_response, 200)["data"]["token"]

    # Create a buyer associated with the user
    buyer_params = Map.put(@buyer_params, :user_id, user.id)
    buyer = Repo.insert!(%Buyer{} |> Buyer.changeset(buyer_params))

    {:ok, user: user, buyer: buyer, token: token}
  end

  describe "index/2" do
    test "returns 400 when unauthenticated", %{conn: conn} do
      conn = get(conn, ~p"/buyers")  # Hardcoded path
      assert json_response(conn, 400)["error"] == %{"message" => "Unauthenticated"}
    end

    test "returns all buyers for authenticated user", %{conn: conn, user: user, token: token} do
      conn = put_req_header(conn, "authorization", "#{token}")
      conn = get(conn, ~p"/buyers")  # Hardcoded path
      assert json_response(conn, 200)["buyers"] != []
    end
  end

#  describe "show/2" do
#    test "returns 400 when unauthenticated", %{conn: conn} do
#      conn = get(conn, ~p"/buyer/#{1}")  # Hardcoded path
#      assert json_response(conn, 400)["error"] == %{"message" => "Unauthenticated"}
#    end
#
#    test "returns buyer details for valid ID", %{conn: conn, user: user, buyer: buyer, token: token} do
#      conn = put_req_header(conn, "authorization", "#{token}")
#      conn = get(conn, ~p"/buyer/#{buyer.id}")  # Hardcoded path
#      assert json_response(conn, 200)["buyer"]["name"] == buyer.name
#    end
#
#    test "returns error for invalid buyer ID", %{conn: conn, user: user, token: token} do
#      conn = put_req_header(conn, "authorization", "#{token}")
#      conn = get(conn, ~p"/buyer/#{-1}")  # Hardcoded path
#      assert json_response(conn, 400)["error"] == %{"message" => "Buyer not found"}
#    end
#  end

  describe "create/2" do
    test "returns 400 when unauthenticated", %{conn: conn} do
      conn = post(conn, ~p"/buyer", @buyer_params)  # Hardcoded path
      assert json_response(conn, 400)["error"] == %{"message" => "Unauthenticated"}
    end

    test "creates a buyer for authenticated user", %{conn: conn, user: user, token: token} do
      conn = put_req_header(conn, "authorization", "#{token}")
      conn = post(conn, ~p"/buyer", @buyer_params)  # Hardcoded path

      response = json_response(conn, 201)["buyer"]

    end
  end

  describe "update/2" do
    test "returns 400 when unauthenticated", %{conn: conn} do
      conn = put(conn, ~p"/buyer/#{1}", @buyer_params)  # Hardcoded path
      assert json_response(conn, 400)["error"] == %{"message" => "Unauthenticated"}
    end

#    test "updates a buyer for valid ID", %{conn: conn, user: user, buyer: buyer, token: token} do
#      updated_params = Map.put(@buyer_params, :first_name, "Updated First Name")
#      conn = put_req_header(conn, "authorization", "#{token}")
#      conn = put(conn, ~p"/buyer/#{buyer.id}", updated_params)  # Hardcoded path
#
#      response = json_response(conn, 200)["buyer"]
#
#      assert response["first_name"] == "Updated First Name"
#      assert response["last_name"] == @buyer_params[:last_name]
#      assert response["buyer_locations_of_interest"] == @buyer_params[:buyer_locations_of_interest]
#    end
  end
end
