<div class="mx-auto max-w-sm">
  <.header class="text-center">
    Log in to account
    <:subtitle>
      Don't have an account?
      <.link navigate={~p"/users/register"} class="font-semibold text-brand hover:underline">
        Sign up
      </.link>
      for an account now.
    </:subtitle>
  </.header>

  <.simple_form :let={f} for={@conn.params["user"]} as={:user} action={~p"/users/log_in"}>
    <.error :if={@error_message}><%= @error_message %></.error>

    <.input field={f[:email]} type="email" label="Email" required />
    <.input field={f[:password]} type="password" label="Password" required />

    <:actions :let={f}>
      <.input field={f[:remember_me]} type="checkbox" label="Keep me logged in" />
      <.link href={~p"/users/reset_password"} class="text-sm font-semibold">
        Forgot your password?
      </.link>
    </:actions>
    <:actions>
      <.button phx-disable-with="Logging in..." class="w-full">
        Log in <span aria-hidden="true">→</span>
      </.button>
    </:actions>
  </.simple_form>
</div>
