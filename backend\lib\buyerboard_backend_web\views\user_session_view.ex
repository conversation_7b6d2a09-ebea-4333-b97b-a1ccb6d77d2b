defmodule BuyerboardBackendWeb.UserSessionView do
  use BuyerboardBackendWeb, :view

  def render("user.json", %{user: user, message: message}) do
    user =
      user
      |> Map.from_struct()
      |> Map.drop([:__meta__, :buyers, :notes, :threads, :auth_providers])

    %{data: user, message: message}
  end

  def render("user.json", %{message: message}), do: %{message: message}

  def render("error.json", %{error: error}), do: %{error: %{message: error}}
end
